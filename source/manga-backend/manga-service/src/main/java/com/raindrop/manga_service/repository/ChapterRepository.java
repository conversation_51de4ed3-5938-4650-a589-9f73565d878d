package com.raindrop.manga_service.repository;

import com.raindrop.manga_service.entity.Chapter;
import com.raindrop.manga_service.entity.Manga;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ChapterRepository extends JpaRepository<Chapter, String> {
    Chapter findByTitle(String title);
    Optional<Chapter> findByMangaAndChapterNumber(Manga manga, double chapterNumber);
    Set<Chapter> findByManga(Manga manga);
    List<Chapter> findByMangaId(String mangaId);
    Page<Chapter> findAll(Pageable pageable);

    int countByMangaId(String mangaId);

    /**
     * T<PERSON>m kiếm và lọc chapter theo manga
     * @param mangaId ID của manga (nếu có)
     * @param pageable Thông tin phân trang
     * @return Danh sách chapter đã được lọc
     */
    @Query("SELECT c FROM Chapter c " +
           "WHERE (:mangaId IS NULL OR c.manga.id = :mangaId) " +
           "ORDER BY c.manga.title ASC, c.chapterNumber ASC")
    Page<Chapter> searchAndFilterChapters(
            @Param("mangaId") String mangaId,
            Pageable pageable);

    /**
     * Tăng lượt xem của chapter mà không cập nhật thời gian updatedAt
     * @param id ID của chapter
     * @return Số bản ghi được cập nhật
     */
    @Modifying
    @Transactional
    @Query("UPDATE Chapter c SET c.views = c.views + 1 WHERE c.id = :id")
    int incrementViews(@Param("id") String id);

    /**
     * Tăng số lượng comment của chapter
     * @param id ID của chapter
     * @return Số bản ghi được cập nhật
     */
    @Modifying
    @Transactional
    @Query("UPDATE Chapter c SET c.comments = c.comments + 1 WHERE c.id = :id")
    int incrementComments(@Param("id") String id);

    /**
     * Giảm số lượng comment của chapter
     * @param id ID của chapter
     * @return Số bản ghi được cập nhật
     */
    @Modifying
    @Transactional
    @Query("UPDATE Chapter c SET c.comments = CASE WHEN c.comments > 0 THEN c.comments - 1 ELSE 0 END WHERE c.id = :id")
    int decrementComments(@Param("id") String id);

    /**
     * Tính tổng số lượt xem của tất cả các chapter của một manga
     * @param mangaId ID của manga
     * @return Tổng số lượt xem
     */
    @Query("SELECT SUM(c.views) FROM Chapter c WHERE c.manga.id = :mangaId")
    Integer sumViewsByMangaId(@Param("mangaId") String mangaId);

    /**
     * Tính t���ng số comment của tất cả các chapter của một manga
     * @param mangaId ID của manga
     * @return Tổng số comment
     */
    @Query("SELECT SUM(c.comments) FROM Chapter c WHERE c.manga.id = :mangaId")
    Integer sumCommentsByMangaId(@Param("mangaId") String mangaId);

    /**
     * Tìm chapter có chapterNumber cao thứ hai của một manga
     * @param mangaId ID của manga
     * @return Danh sách chapter sắp xếp theo chapterNumber giảm dần
     */
    @Query("SELECT c FROM Chapter c WHERE c.manga.id = :mangaId ORDER BY c.chapterNumber DESC")
    List<Chapter> findByMangaIdOrderByChapterNumberDesc(@Param("mangaId") String mangaId);

    // ==================== BATCH QUERIES WITH EAGER LOADING ====================

    /**
     * Tìm tất cả chapters với eager loading pages và manga
     * @param pageable Thông tin phân trang
     * @return Page chapters với pages và manga đã được load
     */
    @Query("SELECT DISTINCT c FROM Chapter c " +
           "LEFT JOIN FETCH c.pages p " +
           "LEFT JOIN FETCH c.manga m " +
           "ORDER BY c.createdAt DESC")
    Page<Chapter> findAllWithPagesAndManga(Pageable pageable);

    /**
     * Tìm chapters theo manga với eager loading pages
     * @param mangaId ID của manga
     * @return List chapters với pages đã được load
     */
    @Query("SELECT DISTINCT c FROM Chapter c " +
           "LEFT JOIN FETCH c.pages p " +
           "WHERE c.manga.id = :mangaId " +
           "ORDER BY c.chapterNumber ASC")
    List<Chapter> findByMangaIdWithPages(@Param("mangaId") String mangaId);

    /**
     * Tìm chapter theo ID với eager loading pages và manga
     * @param id ID của chapter
     * @return Chapter với pages và manga đã được load
     */
    @Query("SELECT c FROM Chapter c " +
           "LEFT JOIN FETCH c.pages p " +
           "LEFT JOIN FETCH c.manga m " +
           "WHERE c.id = :id")
    Optional<Chapter> findByIdWithPagesAndManga(@Param("id") String id);

    /**
     * Search và filter chapters với eager loading
     * @param mangaId ID của manga (nullable)
     * @param pageable Thông tin phân trang
     * @return Page chapters với pages và manga đã được load
     */
    @Query("SELECT DISTINCT c FROM Chapter c " +
           "LEFT JOIN FETCH c.pages p " +
           "LEFT JOIN FETCH c.manga m " +
           "WHERE (:mangaId IS NULL OR c.manga.id = :mangaId) " +
           "ORDER BY c.manga.title ASC, c.chapterNumber ASC")
    Page<Chapter> searchAndFilterChaptersWithPages(
            @Param("mangaId") String mangaId,
            Pageable pageable);

    /**
     * Batch query để lấy page counts cho nhiều chapters
     * @param chapterIds Danh sách chapter IDs
     * @return Map với key là chapterId và value là số pages
     */
    @Query("SELECT p.chapter.id as chapterId, COUNT(p) as pageCount " +
           "FROM Page p WHERE p.chapter.id IN :chapterIds " +
           "GROUP BY p.chapter.id")
    List<Object[]> findPageCountsByChapterIds(@Param("chapterIds") List<String> chapterIds);
}
