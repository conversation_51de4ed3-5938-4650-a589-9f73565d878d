package com.raindrop.manga_service.service;

import com.raindrop.manga_service.dto.response.ApiResponse;
import com.raindrop.manga_service.dto.response.HistoryResponse;
import com.raindrop.manga_service.dto.response.MangaSummaryResponse;
import com.raindrop.manga_service.entity.Genre;
import com.raindrop.manga_service.entity.Manga;
import com.raindrop.manga_service.mapper.MangaMapper;
import com.raindrop.manga_service.repository.ChapterRepository;
import com.raindrop.manga_service.repository.MangaRepository;
import com.raindrop.manga_service.repository.httpclient.HistoryClient;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class RecommendationService {
    MangaRepository mangaRepository;
    ChapterRepository chapterRepository;
    HistoryClient historyClient;
    MangaMapper mangaMapper;


    public List<MangaSummaryResponse> getRecommendationsByGenreSummary(String userId, Integer limit) {
        int recommendationLimit = (limit != null && limit > 0) ? limit : 6;

        try {
            // Lấy lịch sử đọc và extract manga IDs
            List<String> recentMangaIds = getRecentMangaIds(userId);
            if (recentMangaIds.isEmpty()) return Collections.emptyList();

            // Lấy manga details và target genres
            List<Manga> recentMangas = mangaRepository.findAllByIdWithGenres(recentMangaIds);
            if (recentMangas.isEmpty()) return Collections.emptyList();

            // Tìm manga gợi ý với logic fallback thông minh
            List<String> excludeIds = getAllReadMangaIds(userId);
            List<Manga> recommendations = findMangasWithSmartFallback(recentMangas, excludeIds, recommendationLimit);

            return convertToResponses(recommendations);

        } catch (Exception e) {
            log.error("Lỗi gợi ý manga cho user {}: {}", userId, e.getMessage());
            return Collections.emptyList();
        }
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes.getRequest().getHeader("Authorization");
    }

    private List<String> getRecentMangaIds(String userId) {
        try {
            String authHeader = getAuthorizationHeader();
            ApiResponse<List<HistoryResponse>> response = historyClient.getRecentReadingHistory(authHeader, userId, 3);

            if (response.getCode() != 200 || response.getResult().isEmpty()) {
                return Collections.emptyList();
            }

            return response.getResult().stream()
                    .map(HistoryResponse::getMangaId)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi lấy lịch sử đọc: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    private List<String> getAllReadMangaIds(String userId) {
        try {
            String authHeader = getAuthorizationHeader();
            ApiResponse<List<String>> response = historyClient.getAllReadMangaIds(authHeader, userId);

            return (response.getCode() == 200 && response.getResult() != null)
                ? response.getResult()
                : Collections.emptyList();
        } catch (Exception e) {
            log.error("Lỗi lấy tất cả manga đã đọc: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    private List<String> calculateTopGenres(List<Manga> mangas, int topCount) {
        Map<String, Long> genreCounts = mangas.stream()
                .flatMap(manga -> manga.getGenres().stream())
                .map(Genre::getName)
                .collect(Collectors.groupingBy(name -> name, Collectors.counting()));

        // Trả về danh sách thể loại theo thứ tự trọng số giảm dần (để có thể remove từ cuối)
        return genreCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(topCount)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    private List<Manga> findMangasWithSmartFallback(List<Manga> recentMangas, List<String> excludeIds, int limit) {
        // Bắt đầu với 3 thể loại top, nếu không đủ thì giảm dần
        for (int genreCount = 3; genreCount >= 1; genreCount--) {
            List<String> targetGenres = calculateTopGenres(recentMangas, genreCount);
            if (targetGenres.isEmpty()) continue;

            List<Manga> results = findMangasByGenres(targetGenres, excludeIds, limit);

            log.info("Thử với {} thể loại {}: tìm được {} manga",
                    genreCount, targetGenres, results.size());

            // Nếu đủ manga hoặc đã thử với 1 thể loại thì return
            if (results.size() >= limit || genreCount == 1) {
                return results;
            }
        }

        return Collections.emptyList();
    }

    private List<Manga> findMangasByGenres(List<String> genres, List<String> excludeIds, int limit) {
        if (excludeIds.isEmpty()) {
            excludeIds = List.of("no-manga-id"); // Tránh lỗi SQL
        }

        return mangaRepository.findMangasByAllGenres(
                genres, genres.size(), excludeIds, PageRequest.of(0, limit));
    }

    private List<MangaSummaryResponse> convertToResponses(List<Manga> mangas) {
        if (mangas.isEmpty()) return Collections.emptyList();

        // Batch query để lấy lastChapterNumber
        List<String> mangaIds = mangas.stream().map(Manga::getId).collect(Collectors.toList());
        Map<String, Double> chapterMap = getLastChapterNumbers(mangaIds);

        return mangas.stream()
                .map(manga -> {
                    MangaSummaryResponse response = mangaMapper.toMangaSummaryResponse(manga);
                    Double chapterNumber = chapterMap.get(manga.getId());
                    if (chapterNumber != null) {
                        response.setLastChapterNumber(chapterNumber);
                    }
                    return response;
                })
                .collect(Collectors.toList());
    }

    private Map<String, Double> getLastChapterNumbers(List<String> mangaIds) {
        try {
            return mangaRepository.findLastChapterNumbersByMangaIds(mangaIds)
                    .stream()
                    .collect(Collectors.toMap(
                            row -> (String) row[0],
                            row -> (Double) row[1]
                    ));
        } catch (Exception e) {
            log.warn("Lỗi lấy lastChapterNumber: {}", e.getMessage());
            return Collections.emptyMap();
        }
    }


}
