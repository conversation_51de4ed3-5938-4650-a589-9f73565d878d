package com.raindrop.manga_service.service;

import com.raindrop.manga_service.dto.response.ApiResponse;
import com.raindrop.manga_service.dto.response.HistoryResponse;
import com.raindrop.manga_service.dto.response.MangaSummaryResponse;
import com.raindrop.manga_service.entity.Genre;
import com.raindrop.manga_service.entity.Manga;
import com.raindrop.manga_service.mapper.MangaMapper;
import com.raindrop.manga_service.repository.ChapterRepository;
import com.raindrop.manga_service.repository.MangaRepository;
import com.raindrop.manga_service.repository.httpclient.HistoryClient;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class RecommendationService {
    MangaRepository mangaRepository;
    ChapterRepository chapterRepository;
    HistoryClient historyClient;
    MangaMapper mangaMapper;

    @PersistenceContext
    EntityManager entityManager;

    public List<MangaSummaryResponse> getRecommendationsByGenreSummary(String userId, Integer limit) {
        int recommendationLimit = (limit != null && limit > 0) ? limit : 6;

        try {
            // Lấy lịch sử đọc và extract manga IDs
            List<String> recentMangaIds = getRecentMangaIds(userId);
            if (recentMangaIds.isEmpty()) return Collections.emptyList();

            // Lấy manga details và target genres
            List<Manga> recentMangas = mangaRepository.findAllByIdWithGenres(recentMangaIds);
            if (recentMangas.isEmpty()) return Collections.emptyList();

            List<String> targetGenres = calculateTopGenres(recentMangas, 2);
            if (targetGenres.isEmpty()) return Collections.emptyList();

            // Tìm manga gợi ý và chuyển đổi kết quả
            List<String> excludeIds = getAllReadMangaIds(userId);
            List<Manga> recommendations = findMangasByGenres(targetGenres, excludeIds, recommendationLimit);

            return convertToResponses(recommendations);

        } catch (Exception e) {
            log.error("Lỗi gợi ý manga cho user {}: {}", userId, e.getMessage());
            return Collections.emptyList();
        }
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes.getRequest().getHeader("Authorization");
    }

    private List<HistoryResponse> getRecentReadingHistory(String userId, String authHeader) {
        log.info("Gọi API lấy lịch sử đọc gần đây cho người dùng: {}", userId);

        try {
            ApiResponse<List<HistoryResponse>> historyResponse = historyClient.getRecentReadingHistory(authHeader, userId, 3);

            if (historyResponse.getCode() != 200 || historyResponse.getResult().isEmpty()) {
                log.info("Không tìm thấy lịch sử đọc cho người dùng {}, không trả về gợi ý", userId);
                return Collections.emptyList();
            }

            return historyResponse.getResult();

        } catch (Exception e) {
            log.error("Lỗi khi gọi API lấy lịch sử đọc: {}", e.getMessage());
            return Collections.emptyList();
        }
    }


    private List<String> extractMangaIdsFromHistory(List<HistoryResponse> recentHistory) {
        // Tối ưu với parallel stream và LinkedHashSet để maintain order và tránh duplicates
        return recentHistory.parallelStream()
                .map(HistoryResponse::getMangaId)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<String> getAllReadMangaIds(String userId, List<String> recentMangaIds, String authHeader) {
        log.info("Gọi API lấy tất cả mangaId đã đọc cho người dùng: {}", userId);

        try {
            ApiResponse<List<String>> allReadMangaIdsResponse = historyClient.getAllReadMangaIds(authHeader, userId);

            if (allReadMangaIdsResponse.getCode() != 200 || allReadMangaIdsResponse.getResult() == null) {
                log.warn("Không thể lấy tất cả mangaId đã đọc, sử dụng danh sách gần đây");
                return new ArrayList<>(recentMangaIds);
            }

            List<String> allReadMangaIds = allReadMangaIdsResponse.getResult();
            log.info("Lấy được {} mangaId đã đọc của người dùng {}", allReadMangaIds.size(), userId);
            return allReadMangaIds;

        } catch (Exception e) {
            log.error("Lỗi khi gọi API lấy tất cả mangaId đã đọc: {}", e.getMessage());
            return new ArrayList<>(recentMangaIds);
        }
    }

    private List<Manga> getRecentMangasDetails(List<String> recentMangaIds) {
        // Sử dụng optimized query với eager loading để tránh N+1 problem
        List<Manga> recentMangas = mangaRepository.findAllByIdWithGenres(recentMangaIds);

        if (recentMangas.isEmpty()) {
            log.warn("Không tìm thấy thông tin chi tiết cho lịch sử đọc: {}", recentMangaIds);
            return Collections.emptyList();
        }

        // Ghi log thông tin chi tiết về các manga gần đây (tối ưu stream operations)
        recentMangas.forEach(manga -> {
            List<String> genreNames = manga.getGenres().stream()
                    .map(Genre::getName)
                    .collect(Collectors.toList());
            log.info("Manga gần đây: {} (ID: {}), Thể loại: {}", manga.getTitle(), manga.getId(), genreNames);
        });

        return recentMangas;
    }

    private List<String> determineTargetGenres(List<Manga> recentMangas, String userId) {
        // Tính toán trọng số thể loại
        Map<String, Double> genreWeights = calculateGenreWeights(recentMangas);

        // Kiểm tra xem có thể loại nào được tìm thấy không
        if (genreWeights.isEmpty()) {
            log.warn("Không tìm thấy thể loại nào trong các manga gần đây của người dùng {}", userId);

            // Tìm tất cả các thể loại trong hệ thống
            List<String> allGenres = mangaRepository.findAllGenreNames();
            if (allGenres.isEmpty()) {
                log.warn("Không tìm thấy thể loại nào trong hệ thống");
                return Collections.emptyList();
            }

            // Sử dụng tất cả các thể loại với trọng số bằng nhau
            for (String genre : allGenres) {
                genreWeights.put(genre, 1.0 / allGenres.size());
            }

            log.info("Sử dụng tất cả các thể loại với trọng số bằng nhau: {}", genreWeights);
        }

        List<String> targetGenres = genreWeights.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .limit(2) // Chỉ lấy 4 thể loại có trọng số cao nhất
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        log.info("Top {} thể loại ưu tiên cho người dùng {} (theo trọng số): {}",
                targetGenres.size(), userId, targetGenres);

        // DEBUG: Log trọng số của các thể loại được chọn
        targetGenres.forEach(genre -> {
            Double weight = genreWeights.get(genre);
            log.info("  - {}: {}", genre, String.format("%.3f", weight));
        });

        return targetGenres;
    }

    /**
     * Tìm manga tương tự dựa trên thể loại mục tiêu (logic OR - có ít nhất 1 thể loại)
     * Method cũ để backup
     */
    private List<Manga> findSimilarMangas(List<String> targetGenres, List<String> allReadMangaIds, int limit, String userId) {
        // Đảm bảo danh sách excludeMangaIds không trống để tránh lỗi SQL
        if (allReadMangaIds.isEmpty()) {
            allReadMangaIds = new ArrayList<>();
            allReadMangaIds.add("no-manga-id");
        }

        // Gọi truy vấn tìm manga dựa trên thể loại
        List<Manga> recommendedMangas = mangaRepository.findMangasByAllGenres(
                targetGenres, targetGenres.size(), allReadMangaIds, PageRequest.of(0, limit));

        log.info("Tìm thấy {} manga phù hợp với thể loại (logic OR) cho người dùng {}", recommendedMangas.size(), userId);
        return recommendedMangas;
    }

    /**
     * Tìm manga chứa TẤT CẢ các thể loại mục tiêu (logic AND) sử dụng Criteria API với subquery thông minh
     * Áp dụng logic từ AdvancedSearch trong MangaService
     *
     * @param targetGenres    Danh sách thể loại mục tiêu (manga phải chứa TẤT CẢ)
     * @param allReadMangaIds Danh sách mangaId đã đọc để loại trừ
     * @param limit           Số lượng manga cần lấy
     * @param userId          ID của người dùng
     * @return Danh sách manga chứa tất cả thể loại được chỉ định
     */
    private List<Manga> findSimilarMangasWithAllGenres(List<String> targetGenres, List<String> allReadMangaIds, int limit, String userId) {
        // Đảm bảo danh sách excludeMangaIds không trống để tránh lỗi SQL
        if (allReadMangaIds.isEmpty()) {
            allReadMangaIds = new ArrayList<>();
            allReadMangaIds.add("no-manga-id");
        }

        // DEBUG: Log chi tiết input parameters
        log.info("=== DEBUG findSimilarMangasWithAllGenres ===");
        log.info("User ID: {}", userId);
        log.info("Target genres (manga phải chứa TẤT CẢ): {}", targetGenres);
        log.info("Exclude manga IDs: {}", allReadMangaIds);
        log.info("Limit: {}", limit);

        // Nếu chỉ có 1 thể loại, fallback về logic OR để có nhiều kết quả hơn
        if (targetGenres.size() <= 1) {
            log.info("Chỉ có {} thể loại, sử dụng logic OR để có nhiều kết quả hơn", targetGenres.size());
            return findSimilarMangas(targetGenres, allReadMangaIds, limit, userId);
        }

        try {
            // Tạo CriteriaBuilder và CriteriaQuery với eager loading
            CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Manga> query = criteriaBuilder.createQuery(Manga.class);
            Root<Manga> root = query.from(Manga.class);

            // Eager load genres để tránh N+1 problem
            root.fetch("genres", JoinType.LEFT);

            // Danh sách các điều kiện
            List<Predicate> predicates = new ArrayList<>();

            // Điều kiện: manga chưa bị xóa
            predicates.add(criteriaBuilder.equal(root.get("deleted"), false));

            // Điều kiện: loại trừ manga đã đọc
            predicates.add(criteriaBuilder.not(root.get("id").in(allReadMangaIds)));

            // Điều kiện thông minh: manga phải chứa TẤT CẢ các thể loại được chỉ định
            // Sử dụng subquery tối ưu để đếm số lượng thể loại khớp
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<Manga> subRoot = subquery.correlate(root);
            Join<Manga, Genre> genreJoin = subRoot.join("genres", JoinType.INNER);

            subquery.select(criteriaBuilder.count(genreJoin.get("name")))
                    .where(genreJoin.get("name").in(targetGenres));

            // Manga phải chứa đúng số lượng thể loại được yêu cầu
            predicates.add(criteriaBuilder.equal(subquery, (long) targetGenres.size()));

            // Áp dụng tất cả điều kiện
            query.where(criteriaBuilder.and(predicates.toArray(new Predicate[0])));

            // Distinct để tránh duplicate do LEFT JOIN
            query.distinct(true);

            // Sắp xếp theo views giảm dần (ưu tiên manga phổ biến)
            query.orderBy(criteriaBuilder.desc(root.get("views")));

            // Thực hiện truy vấn với limit và hint để tối ưu
            TypedQuery<Manga> typedQuery = entityManager.createQuery(query);
            typedQuery.setMaxResults(limit);

            // Thêm query hints để tối ưu hiệu suất
            typedQuery.setHint("org.hibernate.fetchSize", limit);
            typedQuery.setHint("org.hibernate.readOnly", true);

            List<Manga> recommendedMangas = typedQuery.getResultList();

            // Log kết quả
            log.info("Tìm thấy {} manga chứa TẤT CẢ {} thể loại cho người dùng {}",
                    recommendedMangas.size(), targetGenres.size(), userId);

            if (!recommendedMangas.isEmpty()) {
                log.info("=== CHI TIẾT MANGA TÌM ĐƯỢC (LOGIC AND) ===");
                for (int i = 0; i < recommendedMangas.size(); i++) {
                    Manga manga = recommendedMangas.get(i);
                    List<String> mangaGenres = manga.getGenres().stream()
                            .map(Genre::getName)
                            .collect(Collectors.toList());
                    log.info("{}. Manga: {} (ID: {}), Views: {}, Thể loại: {}",
                        i+1, manga.getTitle(), manga.getId(), manga.getViews(), mangaGenres);

                    // Kiểm tra xem manga này có chứa tất cả thể loại ưu tiên không
                    boolean containsAllGenres = mangaGenres.containsAll(targetGenres);
                    log.info("   -> Chứa TẤT CẢ thể loại yêu cầu: {}", containsAllGenres ? "✓" : "✗");
                }
            } else {
                log.info("Không tìm thấy manga nào chứa TẤT CẢ {} thể loại, fallback về logic OR", targetGenres.size());
                // Fallback về logic OR nếu không tìm thấy manga nào
                return findSimilarMangas(targetGenres, allReadMangaIds, limit, userId);
            }

            return recommendedMangas;

        } catch (Exception e) {
            log.error("Lỗi khi tìm manga với logic AND: {}", e.getMessage(), e);
            log.info("Fallback về logic OR do lỗi");
            // Fallback về logic OR nếu có lỗi
            return findSimilarMangas(targetGenres, allReadMangaIds, limit, userId);
        }
    }

    /**
     * Chuyển đổi danh sách manga sang MangaSummaryResponse với batch query tối ưu
     *
     * @param mangas Danh sách manga cần chuyển đổi
     * @return Danh sách MangaSummaryResponse đã được tối ưu
     */
    private List<MangaSummaryResponse> convertToMangaSummaryResponsesOptimized(List<Manga> mangas) {
        if (mangas.isEmpty()) {
            return Collections.emptyList();
        }

        // Lấy danh sách manga IDs
        List<String> mangaIds = mangas.stream()
                .map(Manga::getId)
                .collect(Collectors.toList());

        // Batch query để lấy lastChapterNumber cho tất cả manga cùng lúc
        Map<String, Double> lastChapterNumberMap = new HashMap<>();
        try {
            List<Object[]> lastChapterData = mangaRepository.findLastChapterNumbersByMangaIds(mangaIds);
            lastChapterData.forEach(row -> {
                String mangaId = (String) row[0];
                Double chapterNumber = (Double) row[1];
                lastChapterNumberMap.put(mangaId, chapterNumber);
            });
        } catch (Exception e) {
            log.warn("Lỗi khi lấy lastChapterNumber batch: {}", e.getMessage());
        }

        // Chuyển đổi sang MangaSummaryResponse với parallel stream để tăng hiệu suất
        return mangas.parallelStream()
                .map(manga -> {
                    MangaSummaryResponse response = mangaMapper.toMangaSummaryResponse(manga);

                    // Bổ sung thông tin lastChapterNumber từ batch query
                    Double lastChapterNumber = lastChapterNumberMap.get(manga.getId());
                    if (lastChapterNumber != null) {
                        response.setLastChapterNumber(lastChapterNumber);
                    }

                    return response;
                })
                .collect(Collectors.toList());
    }

    /**
     * Tính toán trọng số cho từng thể loại dựa trên lịch sử đọc (tối ưu stream operations)
     *
     * @param recentMangas Danh sách manga gần đây
     * @return Map chứa tên thể loại và trọng số tương ứng
     */
    private Map<String, Double> calculateGenreWeights(List<Manga> recentMangas) {
        // Sử dụng stream operations tối ưu để đếm genre
        Map<String, Long> genreCounts = recentMangas.stream()
                .flatMap(manga -> manga.getGenres().stream())
                .map(Genre::getName)
                .collect(Collectors.groupingBy(
                    genreName -> genreName,
                    Collectors.counting()
                ));

        long totalGenres = genreCounts.values().stream()
                .mapToLong(Long::longValue)
                .sum();

        // Tính trọng số với stream operations
        return genreCounts.entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> (double) entry.getValue() / totalGenres
                ));
    }
}
