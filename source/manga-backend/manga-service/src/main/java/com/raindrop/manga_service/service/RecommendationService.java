package com.raindrop.manga_service.service;

import com.raindrop.manga_service.dto.response.ApiResponse;
import com.raindrop.manga_service.dto.response.HistoryResponse;
import com.raindrop.manga_service.dto.response.MangaSummaryResponse;
import com.raindrop.manga_service.entity.Genre;
import com.raindrop.manga_service.entity.Manga;
import com.raindrop.manga_service.mapper.MangaMapper;
import com.raindrop.manga_service.repository.MangaRepository;
import com.raindrop.manga_service.repository.httpclient.HistoryClient;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class RecommendationService {
    MangaRepository mangaRepository;
    HistoryClient historyClient;
    MangaMapper mangaMapper;


    public List<MangaSummaryResponse> getRecommendationsByGenreSummary(String userId, Integer limit) {
        int recommendationLimit = (limit != null && limit > 0) ? limit : 6;

        try {
            // Lấy lịch sử đọc và extract manga IDs
            List<String> recentMangaIds = getRecentMangaIds(userId);
            if (recentMangaIds.isEmpty()) return Collections.emptyList();

            // Lấy manga details và tính toán top genres
            List<Manga> recentMangas = mangaRepository.findAllByIdWithGenres(recentMangaIds);
            if (recentMangas.isEmpty()) return Collections.emptyList();

            List<String> topGenres = calculateTopGenres(recentMangas, 3);
            if (topGenres.isEmpty()) return Collections.emptyList();

            log.info("Top genres cho user {}: {}", userId, topGenres);

            // Tìm manga gợi ý với logic fallback thông minh
            List<String> excludeIds = getAllReadMangaIds(userId);
            List<Manga> recommendations = findMangasWithSmartFallback(topGenres, excludeIds, recommendationLimit);

            return convertToResponses(recommendations);

        } catch (Exception e) {
            log.error("Lỗi gợi ý manga cho user {}: {}", userId, e.getMessage());
            return Collections.emptyList();
        }
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes.getRequest().getHeader("Authorization");
    }

    private List<String> getRecentMangaIds(String userId) {
        try {
            String authHeader = getAuthorizationHeader();
            ApiResponse<List<HistoryResponse>> response = historyClient.getRecentReadingHistory(authHeader, userId, 3);

            if (response.getCode() != 200 || response.getResult().isEmpty()) {
                return Collections.emptyList();
            }

            return response.getResult().stream()
                    .map(HistoryResponse::getMangaId)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Lỗi lấy lịch sử đọc: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    private List<String> getAllReadMangaIds(String userId) {
        try {
            String authHeader = getAuthorizationHeader();
            ApiResponse<List<String>> response = historyClient.getAllReadMangaIds(authHeader, userId);

            return (response.getCode() == 200 && response.getResult() != null)
                ? response.getResult()
                : Collections.emptyList();
        } catch (Exception e) {
            log.error("Lỗi lấy tất cả manga đã đọc: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    private List<String> calculateTopGenres(List<Manga> mangas, int topCount) {
        Map<String, Long> genreCounts = mangas.stream()
                .flatMap(manga -> manga.getGenres().stream())
                .map(Genre::getName)
                .collect(Collectors.groupingBy(name -> name, Collectors.counting()));

        // Trả về danh sách thể loại theo thứ tự trọng số giảm dần (để có thể remove từ cuối)
        return genreCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(topCount)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    private List<Manga> findMangasWithSmartFallback(List<String> topGenres, List<String> excludeIds, int limit) {
        Map<String, Manga> found = new LinkedHashMap<>();
        Set<String> exclude = new HashSet<>(excludeIds);

        for (int genreCount = topGenres.size(); genreCount > 0 && found.size() < limit; genreCount--) {
            List<String> genres = topGenres.subList(0, genreCount);
            List<Manga> results = findMangasByGenres(genres, new ArrayList<>(exclude), limit - found.size());

            results.stream()
                    .filter(manga -> !found.containsKey(manga.getId()))
                    .forEach(manga -> {
                        found.put(manga.getId(), manga);
                        exclude.add(manga.getId());
                    });

            log.info("Với {} thể loại {}: +{} manga (tổng: {})",
                    genreCount, genres, results.size(), found.size());
        }

        return new ArrayList<>(found.values());
    }

    private List<Manga> findMangasByGenres(List<String> genres, List<String> excludeIds, int limit) {
        if (excludeIds.isEmpty()) {
            excludeIds = List.of("no-manga-id"); // Tránh lỗi SQL
        }

        return mangaRepository.findMangasByAllGenres(
                genres, genres.size(), excludeIds, PageRequest.of(0, limit));
    }

    private List<MangaSummaryResponse> convertToResponses(List<Manga> mangas) {
        if (mangas.isEmpty()) return Collections.emptyList();

        // Batch query để lấy lastChapterNumber
        List<String> mangaIds = mangas.stream().map(Manga::getId).collect(Collectors.toList());
        Map<String, Double> chapterMap = getLastChapterNumbers(mangaIds);

        return mangas.stream()
                .map(manga -> {
                    MangaSummaryResponse response = mangaMapper.toMangaSummaryResponse(manga);
                    Double chapterNumber = chapterMap.get(manga.getId());
                    if (chapterNumber != null) {
                        response.setLastChapterNumber(chapterNumber);
                    }
                    return response;
                })
                .collect(Collectors.toList());
    }

    private Map<String, Double> getLastChapterNumbers(List<String> mangaIds) {
        try {
            return mangaRepository.findLastChapterNumbersByMangaIds(mangaIds)
                    .stream()
                    .collect(Collectors.toMap(
                            row -> (String) row[0],
                            row -> (Double) row[1]
                    ));
        } catch (Exception e) {
            log.warn("Lỗi lấy lastChapterNumber: {}", e.getMessage());
            return Collections.emptyMap();
        }
    }


}
